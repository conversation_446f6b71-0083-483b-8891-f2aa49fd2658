{"graph_nodes": 15, "graph_edges": 59, "total_pairs": 210, "final_train_pairs": 77, "validation_pairs": 42, "best_prob_threshold": 0.8, "target_accuracy": 0.99, "final_val_accuracy": 0.8571428571428571, "iterations": [{"iteration": 1, "train_size": 27, "val_accuracy": 0.6904761904761905, "epochs_trained": 1100, "duration_sec": 46.50425601005554, "prob_threshold": 0.8}, {"iteration": 2, "train_size": 37, "val_accuracy": 0.7142857142857143, "epochs_trained": 1000, "duration_sec": 69.36313533782959, "prob_threshold": 0.5}, {"iteration": 3, "train_size": 47, "val_accuracy": 0.7619047619047619, "epochs_trained": 1800, "duration_sec": 140.80286931991577, "prob_threshold": 0.3}, {"iteration": 4, "train_size": 57, "val_accuracy": 0.8095238095238095, "epochs_trained": 1500, "duration_sec": 127.30509757995605, "prob_threshold": 0.3}, {"iteration": 5, "train_size": 67, "val_accuracy": 0.8571428571428571, "epochs_trained": 1500, "duration_sec": 167.99584794044495, "prob_threshold": 0.8}]}