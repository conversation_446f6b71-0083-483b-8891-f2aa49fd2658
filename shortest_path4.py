import torch
import torch.nn.functional as F
import networkx as nx
import matplotlib.pyplot as plt
from torch_geometric.nn import SAGEConv
from torch_geometric.data import Data
from torch_geometric.utils import from_networkx
import itertools
import random
from sklearn.model_selection import train_test_split

from torch_geometric.loader import DataLoader
import sys
import copy

# --- 1. Setup: Create a single, fixed graph and problem set ---

# Create a strongly connected directed graph with positive edge weights
def make_directed_weighted_graph(num_nodes=15, prob=0.3, seed=42, directed=True):
    rng = random.Random(seed)
    while True:
        g = nx.gnp_random_graph(num_nodes, prob, seed=rng.randint(0, 10_000), directed=directed)
        if directed:
            if nx.is_strongly_connected(g):
                break
        else:
            if nx.is_connected(g):
                break
    for u, v in g.edges:
        g[u][v]["weight"] = rng.uniform(0.5, 2.0)
    return g


FIXED_GRAPH = make_directed_weighted_graph()
REVERSED_GRAPH = FIXED_GRAPH.reverse()

# Older torch_geometric releases don't accept `edge_attrs` when converting from
# NetworkX, so we manually extract the weights in the same edge order.
pyg_graph = from_networkx(FIXED_GRAPH)
edge_index = pyg_graph.edge_index
edge_weight = torch.tensor(
    [FIXED_GRAPH[u][v]["weight"] for u, v in edge_index.t().tolist()],
    dtype=torch.float,
)
EDGE_COUNT = edge_index.size(1)
NODE_COUNT = FIXED_GRAPH.number_of_nodes()

# Generate all possible (start, end) pairs
all_nodes = list(FIXED_GRAPH.nodes)
all_possible_pairs = list(itertools.permutations(all_nodes, 2))

# Split pairs into a training set and a testing set
train_pairs, test_pairs = train_test_split(all_possible_pairs, test_size=0.2, random_state=42)
train_pairs, val_pairs = train_test_split(train_pairs, test_size=0.15, random_state=24)

def describe_split(prefix="--- Graph Setup ---"):
    print(prefix)
    print(f"Fixed graph has {FIXED_GRAPH.number_of_nodes()} nodes and {FIXED_GRAPH.number_of_edges()} edges.")
    print(f"Training on {len(train_pairs)} start/end pairs.")
    print(f"Validation on {len(val_pairs)} start/end pairs.")
    print(f"Testing on {len(test_pairs)} unseen start/end pairs.")


# --- 2. Helper Function to Create a Specific Problem Instance ---
def generate_data(nx_graph, edge_index, start_node, end_node):
    # Calculate the weighted shortest path for this specific problem
    path_nodes = nx.shortest_path(nx_graph, source=start_node, target=end_node, weight="weight")
    path_edges = set(zip(path_nodes[:-1], path_nodes[1:]))
    path_dists = nx.shortest_path_length(nx_graph, source=start_node, target=end_node, weight="weight")

    # Create edge labels (y)
    edge_labels = torch.zeros(EDGE_COUNT, dtype=torch.float)
    for i, (u, v) in enumerate(edge_index.t().tolist()):
        if (u, v) in path_edges:
            edge_labels[i] = 1.0

    # Create node features (x) with start/end indicators and weighted distance heuristics
    node_features = torch.zeros((nx_graph.number_of_nodes(), 2))

    # One-hot indicators for query endpoints
    node_features[start_node, 0] = 1.0
    node_features[end_node, 1] = 1.0
    
    """
    # Dijkstra distances from the start node (normalized per instance)
    start_distances = nx.single_source_dijkstra_path_length(nx_graph, start_node, weight="weight")
    max_start_distance = max(start_distances.values()) if start_distances else 1.0
    start_dist_tensor = torch.tensor([
        start_distances.get(node_idx, max_start_distance) for node_idx in range(NODE_COUNT)
    ], dtype=torch.float)
    start_dist_raw = start_dist_tensor.clone()
    start_dist_denom = max(start_dist_tensor.max().item(), 1e-6)
    start_dist_vector = start_dist_tensor / start_dist_denom

    # Dijkstra distances to the end node by traversing the reversed graph
    end_distances = nx.single_source_dijkstra_path_length(REVERSED_GRAPH, end_node, weight="weight")
    max_end_distance = max(end_distances.values()) if end_distances else 1.0
    end_dist_tensor = torch.tensor([
        end_distances.get(node_idx, max_end_distance) for node_idx in range(NODE_COUNT)
    ], dtype=torch.float)
    end_dist_raw = end_dist_tensor.clone()
    end_dist_denom = max(end_dist_tensor.max().item(), 1e-6)
    end_dist_vector = end_dist_tensor / end_dist_denom

    # Combined potential via sum of normalized distances (low value near true path)
    sum_vector = start_dist_vector + end_dist_vector
    sum_denom = max(sum_vector.max().item(), 1e-6)
    sum_vector = sum_vector / sum_denom

    # Edge-level margin relative to optimal path cost
    target_distance = start_dist_raw[end_node].item()
    edge_margins = []
    for u, v in edge_index.t().tolist():
        edge_cost = start_dist_raw[u].item() + FIXED_GRAPH[u][v]["weight"] + end_dist_raw[v].item()
        edge_margins.append(target_distance - edge_cost)
    edge_margin = torch.tensor(edge_margins, dtype=torch.float)
    if target_distance > 0.0:
        edge_margin = edge_margin / target_distance

    node_features[:, 2] = start_dist_vector
    node_features[:, 3] = end_dist_vector
    node_features[:, 4] = sum_vector
    """

    return Data(
        x=node_features,
        edge_index=edge_index.clone(),
        edge_weight=edge_weight.clone(),
        #edge_margin=edge_margin.clone(),
        y=edge_labels,
        distance=path_dists,
        pair=torch.tensor([start_node, end_node], dtype=torch.long),
    )

# --- 3. GNN Model Definition (No changes here) ---
class PathPredictorGNN(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels):
        super(PathPredictorGNN, self).__init__()
        self.conv1 = SAGEConv(in_channels, hidden_channels)
        self.conv2 = SAGEConv(hidden_channels, hidden_channels)
        self.edge_predictor = torch.nn.Sequential(
            torch.nn.Linear(2 * hidden_channels + 1, hidden_channels),
            torch.nn.ReLU(),
            torch.nn.Dropout(p=0.1),
            torch.nn.Linear(hidden_channels, out_channels),
        )
        self.margin_bias = torch.nn.Parameter(torch.tensor(0.0))

    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        edge_weight = data.edge_weight
        # Now using edge weights directly in message passing
        x = F.relu(self.conv1(x, edge_index, edge_weight))
        x = F.relu(self.conv2(x, edge_index, edge_weight))
        edge_src, edge_dst = edge_index
        edge_weight_feat = edge_weight.unsqueeze(-1)
        # Concatenate node embeddings and edge weight features
        edge_features = torch.cat([x[edge_src], x[edge_dst], edge_weight_feat], dim=1)
        residual = self.edge_predictor(edge_features).view(-1)
        return residual + self.margin_bias

# --- 4a. Regularizers ---
def soft_connectivity_penalty(batch, edge_probs, max_steps=None, clamp_prob=1.0):
    """Penalize predicted edge sets that fail to reach the target node."""
    num_graphs = batch.num_graphs
    if num_graphs == 0:
        return torch.tensor(0.0, device=edge_probs.device)

    if max_steps is None:
        max_steps = NODE_COUNT

    edge_probs = edge_probs.view(num_graphs, EDGE_COUNT)

    adj = torch.zeros((num_graphs, NODE_COUNT * NODE_COUNT), device=edge_probs.device, dtype=edge_probs.dtype)
    rows = edge_index[0].to(edge_probs.device)
    cols = edge_index[1].to(edge_probs.device)
    flat_indices = rows * NODE_COUNT + cols
    adj[:, flat_indices] = torch.clamp(edge_probs, 0.0, clamp_prob)
    adj = adj.view(num_graphs, NODE_COUNT, NODE_COUNT)

    pairs = batch.pair
    if pairs.dim() == 1:
        pairs = pairs.unsqueeze(0)

    start = torch.nn.functional.one_hot(pairs[:, 0], num_classes=NODE_COUNT).float().to(adj.device)
    end = torch.nn.functional.one_hot(pairs[:, 1], num_classes=NODE_COUNT).float().to(adj.device)

    reached = start.clone()
    frontier = start.clone()

    for _ in range(max_steps):
        frontier = torch.einsum('bi,bij->bj', frontier, adj)
        reached = torch.clamp(reached + frontier, max=1.0)
        frontier = torch.clamp(frontier, max=1.0)

    connectivity = (reached * end).sum(dim=1)
    penalty = 1.0 - connectivity
    return penalty.mean()


def path_length_penalty(batch, edge_probs):
    """Encourage predicted edge counts to match ground-truth path length."""
    num_graphs = batch.num_graphs
    if num_graphs == 0:
        return torch.tensor(0.0, device=edge_probs.device)

    edge_probs = edge_probs.view(num_graphs, EDGE_COUNT)
    labels = batch.y.view(num_graphs, EDGE_COUNT)

    target_lengths = labels.sum(dim=1)
    predicted_lengths = edge_probs.sum(dim=1)

    return F.mse_loss(predicted_lengths, target_lengths)

# --- 4b. Evaluation helper ---
@torch.no_grad()
def evaluate_model(model, loader, prob_threshold=0.5, device=None):
    model.eval()
    correct_predictions = 0

    for batch in loader:
        if device is not None:
            batch = batch.to(device)
        edge_logits = model(batch)
        edge_probs = torch.sigmoid(edge_logits)
        predicted_edges = (edge_probs >= prob_threshold).float()
        labels = batch.y

        batch_size = batch.num_graphs
        preds_per_graph = predicted_edges.view(batch_size, EDGE_COUNT)
        labels_per_graph = labels.view(batch_size, EDGE_COUNT)

        matches = torch.all(preds_per_graph == labels_per_graph, dim=1)
        correct_predictions += matches.sum().item()

    accuracy = correct_predictions / len(loader.dataset)
    return accuracy

def train_model(max_epochs=5000, lr=0.003, hidden_channels=64, connectivity_weight=0.1,
                length_weight=0.1, eval_interval=100, patience=8, weight_decay=0.0,
                prob_threshold_grid=None, batch_size=32, val_batch_size=64, test_batch_size=64,
                target_accuracy=0.99):
    if prob_threshold_grid is None:
        prob_threshold_grid = torch.linspace(0.05, 0.95, steps=19)

    train_dataset = [generate_data(FIXED_GRAPH, edge_index, s, e) for s, e in train_pairs]
    val_dataset = [generate_data(FIXED_GRAPH, edge_index, s, e) for s, e in val_pairs]
    test_dataset = [generate_data(FIXED_GRAPH, edge_index, s, e) for s, e in test_pairs]

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=val_batch_size)
    test_loader = DataLoader(test_dataset, batch_size=test_batch_size)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = PathPredictorGNN(in_channels=2, hidden_channels=hidden_channels, out_channels=1).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)

    total_pos = sum(data.y.sum().item() for data in train_dataset)
    total_edges = len(train_dataset) * EDGE_COUNT
    total_neg = max(total_edges - total_pos, 1.0)
    pos_weight = torch.tensor(total_neg / max(total_pos, 1.0), dtype=torch.float, device=device)
    bce_loss = torch.nn.BCEWithLogitsLoss(pos_weight=pos_weight)
    
    best_val_accuracy = 0.0
    best_state = None

    print("\n--- Starting Training ---")
    final_epoch = 0
    for epoch in range(1, max_epochs + 1):
        model.train()
        total_loss = 0.0

        for batch in train_loader:
            batch = batch.to(device)
            optimizer.zero_grad()
            edge_logits = model(batch)
            edge_probs = torch.sigmoid(edge_logits)
            loss = (
                bce_loss(edge_logits, batch.y)
                + connectivity_weight * soft_connectivity_penalty(batch, edge_probs)
                + length_weight * path_length_penalty(batch, edge_probs)
            )
            loss.backward()
            optimizer.step()
            total_loss += loss.item()

        avg_loss = total_loss / len(train_loader)
        if epoch % 100 == 0:
            print(f"Epoch: {epoch:03d}, Avg. Training Loss: {avg_loss:.4f}")

        final_epoch = epoch
        if epoch % eval_interval == 0:
            val_accuracy = evaluate_model(model, val_loader, prob_threshold=0.5, device=device)
            improved = val_accuracy > best_val_accuracy + 1e-5
            status = "*" if improved else ""
            print(f"Epoch: {epoch:03d}, Val Accuracy: {val_accuracy:.2%}{status}")
            if improved:
                best_val_accuracy = val_accuracy
                best_state = copy.deepcopy(model.state_dict())

            if val_accuracy >= target_accuracy:
                print(f"Target validation accuracy {target_accuracy:.2%} reached at epoch {epoch}.")
                best_val_accuracy = val_accuracy
                best_state = copy.deepcopy(model.state_dict())
                break

    print("--- Training Finished ---")

    if best_state is not None:
        model.load_state_dict(best_state)

    best_prob_threshold = 0.5
    best_val_acc = evaluate_model(model, val_loader, prob_threshold=best_prob_threshold, device=device)
    for threshold in prob_threshold_grid.tolist():
        val_acc = evaluate_model(model, val_loader, prob_threshold=float(threshold), device=device)
        if val_acc > best_val_acc + 1e-6:
            best_val_acc = val_acc
            best_prob_threshold = float(threshold)

    print(
        f"Selected probability threshold: {best_prob_threshold:.2f} "
        f"(val accuracy {best_val_acc:.2%})"
    )

    test_accuracy = evaluate_model(
        model,
        test_loader,
        prob_threshold=best_prob_threshold,
        device=device,
    )
    print(f"\n--- Evaluation Results ---")
    print(f"Accuracy on unseen test pairs: {test_accuracy:.2%}")

    return {
        "model": model,
        "best_prob_threshold": best_prob_threshold,
        "val_accuracy": best_val_acc,
        "test_accuracy": test_accuracy,
        "epochs_trained": final_epoch,
    }


def visualize_predictions(model, prob_threshold=0.5, test_pairs_subset=None, device=None):
    print("Visualizing random test cases. Press Ctrl-C to stop...")
    pos = nx.spring_layout(FIXED_GRAPH, seed=42)

    if test_pairs_subset is None:
        test_pairs_subset = test_pairs

    try:
        while True:
            start, end = random.choice(test_pairs_subset)
            data = generate_data(FIXED_GRAPH, edge_index, start, end)
            if device is not None:
                data = data.to(device)
            edge_logits = model(data)
            edge_probs = torch.sigmoid(edge_logits)
            edge_mask = edge_probs >= prob_threshold
            predicted_edges = edge_index[:, edge_mask].t().tolist()
            actual_path_nodes = nx.shortest_path(FIXED_GRAPH, source=start, target=end)

            plt.figure(figsize=(12, 5))

            plt.subplot(1, 2, 1)
            nx.draw(FIXED_GRAPH, pos, with_labels=True, node_color='lightblue', edge_color='gray')
            path_edges = list(zip(actual_path_nodes, actual_path_nodes[1:]))
            nx.draw_networkx_edges(FIXED_GRAPH, pos, edgelist=path_edges, edge_color='green', width=3)
            nx.draw_networkx_nodes(FIXED_GRAPH, pos, nodelist=[start, end], node_color='green')
            plt.title(f'Ground Truth: {start} to {end}')

            plt.subplot(1, 2, 2)
            nx.draw(FIXED_GRAPH, pos, with_labels=True, node_color='lightblue', edge_color='gray')
            nx.draw_networkx_edges(FIXED_GRAPH, pos, edgelist=predicted_edges, edge_color='red', width=3)
            nx.draw_networkx_nodes(FIXED_GRAPH, pos, nodelist=[start, end], node_color='red')
            plt.title(f'GNN Prediction: {start} to {end}')

            plt.show()

    except KeyboardInterrupt:
        print("\nVisualization stopped by user.")


def main():
    describe_split()
    train_model()
    sys.exit(0)


if __name__ == "__main__":
    main()
