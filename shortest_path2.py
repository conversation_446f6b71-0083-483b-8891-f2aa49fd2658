import torch
import torch.nn.functional as F
import networkx as nx
import matplotlib.pyplot as plt
from torch_geometric.nn import SAGEConv
from torch_geometric.data import Data
from torch_geometric.utils import from_networkx, to_networkx
import itertools
from sklearn.model_selection import train_test_split
import random
import sys

# --- 1. Setup: Create a single, fixed graph and problem set ---

# Create the fixed graph that will be used for everything
FIXED_GRAPH = nx.erdos_renyi_graph(n=15, p=0.3, seed=42)
while not nx.is_connected(FIXED_GRAPH): # Ensure it's connected
    FIXED_GRAPH = nx.erdos_renyi_graph(n=15, p=0.3)
edge_index = from_networkx(FIXED_GRAPH).edge_index

# Generate all possible (start, end) pairs
all_nodes = list(FIXED_GRAPH.nodes)
all_possible_pairs = list(itertools.permutations(all_nodes, 2))

# Split pairs into a training set and a testing set
train_pairs, test_pairs = train_test_split(all_possible_pairs, test_size=0.2, random_state=42)

print(f"--- Graph Setup ---")
print(f"Fixed graph has {FIXED_GRAPH.number_of_nodes()} nodes and {FIXED_GRAPH.number_of_edges()} edges.")
print(f"Training on {len(train_pairs)} start/end pairs.")
print(f"Testing on {len(test_pairs)} unseen start/end pairs.")

# --- 2. Helper Function to Create a Specific Problem Instance ---
def generate_data(nx_graph, edge_index, start_node, end_node):
    # Calculate the shortest path for this specific problem
    path_nodes = nx.shortest_path(nx_graph, source=start_node, target=end_node)
    path_edges = set(zip(path_nodes[:-1], path_nodes[1:]))

    # Create edge labels (y)
    edge_labels = torch.zeros(edge_index.size(1), dtype=torch.float)
    for i, (u, v) in enumerate(edge_index.t().tolist()):
        if (u, v) in path_edges or (v, u) in path_edges:
            edge_labels[i] = 1.0
            
    # Create node features (x) with start/end indicators
    node_features = torch.zeros((nx_graph.number_of_nodes(), 2))
    node_features[start_node, 0] = 1.0
    node_features[end_node, 1] = 1.0
    
    return Data(x=node_features, y=edge_labels)

# --- 3. GNN Model Definition (No changes here) ---
class PathPredictorGNN(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels, edge_index):
        super(PathPredictorGNN, self).__init__()
        self.edge_index = edge_index
        self.conv1 = SAGEConv(in_channels, hidden_channels)
        self.conv2 = SAGEConv(hidden_channels, hidden_channels)
        self.edge_predictor = torch.nn.Sequential(
            torch.nn.Linear(2 * hidden_channels, hidden_channels),
            torch.nn.ReLU(),
            torch.nn.Linear(hidden_channels, out_channels),
            torch.nn.Sigmoid()
        )

    def forward(self, data):
        x = data.x
        x = F.relu(self.conv1(x, self.edge_index))
        x = F.relu(self.conv2(x, self.edge_index))
        edge_src, edge_dst = self.edge_index
        edge_features = torch.cat([x[edge_src], x[edge_dst]], dim=1)
        return self.edge_predictor(edge_features).squeeze()

# --- 4. Training Loop ---
model = PathPredictorGNN(in_channels=2, hidden_channels=32, out_channels=1, edge_index=edge_index)
optimizer = torch.optim.Adam(model.parameters(), lr=0.005)
criterion = torch.nn.BCELoss()

print("\n--- Starting Training ---")
for epoch in range(1, 1001): # Fewer epochs needed as each epoch is much longer
    model.train()
    total_loss = 0
    random.shuffle(train_pairs) # Shuffle for better training
    
    for start_node, end_node in train_pairs:
        optimizer.zero_grad()
        data = generate_data(FIXED_GRAPH, edge_index, start_node, end_node)
        edge_predictions = model(data)
        loss = criterion(edge_predictions, data.y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    
    avg_loss = total_loss / len(train_pairs)
    if epoch % 10 == 0:
        print(f"Epoch: {epoch:03d}, Avg. Training Loss: {avg_loss:.4f}")

print("--- Training Finished ---")

# --- 5. Evaluation on the Unseen Test Set ---
@torch.no_grad()
def evaluate_model(model, nx_graph, edge_index, pairs_to_test):
    model.eval()
    correct_predictions = 0
    
    for start_node, end_node in pairs_to_test:
        data = generate_data(nx_graph, edge_index, start_node, end_node)
        edge_probs = model(data)
        
        predicted_edges = edge_index[:, edge_probs > 0.5].t().tolist()

        #import ipdb; ipdb.set_trace()
        edge_labels = torch.zeros(edge_index.size(1), dtype=torch.float)
        for i, (u, v) in enumerate(edge_index.t().tolist()):
            if [u, v] in predicted_edges or [v, u] in predicted_edges:
                edge_labels[i] = 1.0

        if torch.equal(edge_labels, data.y):
            correct_predictions += 1
            
    accuracy = correct_predictions / len(pairs_to_test)
    return accuracy


accuracy = evaluate_model(model, FIXED_GRAPH, edge_index, test_pairs)
print(f"\n--- Evaluation Results ---")
print(f"Accuracy on unseen test pairs: {accuracy:.2%}")

# --- 6. Visualize random test cases in a loop ---
print("Visualizing random test cases. Press Ctrl-C to stop...")
pos = nx.spring_layout(FIXED_GRAPH, seed=42)  # Keep consistent layout

try:
    while True:
        start, end = random.choice(test_pairs)
        data = generate_data(FIXED_GRAPH, edge_index, start, end)
        edge_probs = model(data)
        predicted_edges = edge_index[:, edge_probs > 0.5].t().tolist()
        actual_path_nodes = nx.shortest_path(FIXED_GRAPH, source=start, target=end)

        plt.figure(figsize=(12, 5))

        # Plot Ground Truth
        plt.subplot(1, 2, 1)
        nx.draw(FIXED_GRAPH, pos, with_labels=True, node_color='lightblue', edge_color='gray')
        path_edges = list(zip(actual_path_nodes, actual_path_nodes[1:]))
        nx.draw_networkx_edges(FIXED_GRAPH, pos, edgelist=path_edges, edge_color='green', width=3)
        nx.draw_networkx_nodes(FIXED_GRAPH, pos, nodelist=[start, end], node_color='green')
        plt.title(f'Ground Truth: {start} to {end}')

        # Plot GNN Prediction
        plt.subplot(1, 2, 2)
        nx.draw(FIXED_GRAPH, pos, with_labels=True, node_color='lightblue', edge_color='gray')
        nx.draw_networkx_edges(FIXED_GRAPH, pos, edgelist=predicted_edges, edge_color='red', width=3)
        nx.draw_networkx_nodes(FIXED_GRAPH, pos, nodelist=[start, end], node_color='red')
        plt.title(f'GNN Prediction: {start} to {end}')

        plt.show()

        # Optional: add a small delay between visualizations
        # time.sleep(1)

except KeyboardInterrupt:
    print("\nVisualization stopped by user.")