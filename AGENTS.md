# Repository Guidelines

## Project Structure & Module Organization
- Core scripts sit at the repo root: `main.py` is a lightweight sanity entry point, while `shortest_path.py` and `shortest_path2.py` host the graph-generation logic, GNN models, and visualization loops.
- Model outputs such as figures go next to their producer (`shortest_path_result.png` today); keep generated artifacts in a dedicated `artifacts/` or `reports/` folder when they accumulate.
- Configuration lives in `pyproject.toml` (Python 3.12+, Torch + Torch Geometric stack). Add new utilities under descriptive snake_case filenames and stage future automated tests in `tests/`.

## Environment & Dependency Setup
- Use [uv](https://astral.sh/uv) with the checked-in `uv.lock` for deterministic installs: `uv sync` provisions the virtual environment and pins CUDA-compatible Torch wheels.
- Activate a shell with `uv run` or `uv pip install <pkg>` for ad-hoc tooling. Prefer CPU-friendly defaults; declare GPU-specific requirements in `pyproject.toml` extras before using them.

## Build, Test, and Development Commands
- `uv run python main.py` – quick smoke check that the environment resolves and imports succeed.
- `uv run python shortest_path.py` – trains the per-epoch regenerated graph model and prints loss every 200 epochs.
- `uv run python shortest_path2.py` – benchmarks on held-out start/end pairs and launches matplotlib visual diagnostics (set `MPLBACKEND=Agg` when running headless).

## Coding Style & Naming Conventions
- Follow PEP 8: four-space indents, `snake_case` for functions/modules, `PascalCase` for classes (e.g., `PathPredictorGNN`). Keep tensors in lowercase nouns (`edge_index`, `node_features`).
- Include module-level docstrings for new scripts and inline comments only for non-obvious tensor ops or training heuristics.
- Prefer explicit device handling (`device = torch.device(...)`) before scaling beyond CPU.

## Testing Guidelines
- Adopt `pytest` for future automation; place files under `tests/` mirroring the script names (`tests/test_shortest_path.py`).
- Until tests exist, validate changes via the run commands above and snapshot key metrics (loss, accuracy) alongside seed values in PR descriptions.

## Commit & Pull Request Guidelines
- Use short, imperative commit subjects similar to `first version undirected graph`; scope body lines to describe model, data, and visualization impacts.
- Reference related issues in the PR description, summarize runtime/testing evidence, and attach updated plots or logs when behavior changes.
- Request review when the branch cleanly installs with `uv sync` and reproduces the stated metrics.
