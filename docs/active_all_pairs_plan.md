# Active Training for All-Pairs Shortest-Path Prediction

## Requirements
- **Graph assumptions**: Directed, positively weighted, static edge weights. Initial experiments on toy graphs (dozens of nodes) with a path to scale toward real road networks (thousands of nodes/edges).
- **Model stack**: Reuse existing PyTorch Geometric GNN from `shortest_path4.py`. Training runs on CPU for toy graphs; design to support GPU for larger instances.
- **Accuracy target**: ~100% exact-path match on a held-out validation set before deploying the model for inference on the remaining OD pairs.
- **Efficiency goal**: Minimize the number of OD pairs that require Dijkstra labeling. Optimise inference throughput by batching OD pairs.
- **Outputs/artifacts**:
  - Trained model weights (`state_dict`).
  - Selected margin threshold (or other inference parameters).
  - Predictions for every origin-destination pair in a simple path format.
  - Optional training log/metrics for traceability.

## Approach Overview
1. **Reusable utilities**
   - Import graph construction, data generation, and `PathPredictorGNN` from `shortest_path4.py` without triggering the script entry point.
   - Build helper functions to materialize `torch_geometric` datasets for arbitrary OD lists and to run batched inference.

2. **Dataset partitioning**
   - Enumerate all OD permutations for the fixed graph.
   - Reserve a validation split (e.g., 10–20% stratified by path length) that is fully labeled before training.
   - Keep the remaining OD pairs in an unlabeled pool.

3. **Active learning loop**
   - Start with a coverage-aware seed training set (ensure each node appears as source/destination at least once).
   - Iterate:
     1. Train the GNN on the current labeled subset with early stopping and margin-threshold search.
     2. Evaluate on the labeled validation set; if accuracy ≥ target, stop.
     3. Otherwise, acquire a batch of new OD pairs:
        - Sample a candidate pool of unlabeled pairs.
        - Score each by running the current model, comparing predicted edge masks to Dijkstra paths, and select the worst mismatches.
        - Label these via Dijkstra and move them from the pool to the training set.

4. **Final inference**
   - After the accuracy target is met, run batched inference on all remaining OD pairs using the trained model and chosen margin threshold.
   - Optionally verify a random subset via Dijkstra as a sanity check before saving predictions.

5. **Persistence and reporting**
   - Save the trained `state_dict`, margin threshold, and configuration metadata (JSON/YAML).
   - Write all-pairs predictions to a JSONL or CSV file containing origin, destination, and node sequence.
   - Log iteration metrics (training subset size, validation accuracy, acquisition details) for reproducibility.

6. **Scalability considerations**
   - Allow hyperparameters (candidate pool size, acquisition batch, batch size, epochs) to be tuned via CLI args or config file.
   - For large graphs, support GPU acceleration and streaming of unlabeled OD pools to avoid loading everything in memory at once.

