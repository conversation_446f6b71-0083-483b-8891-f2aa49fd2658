import torch
import torch.nn.functional as F
import networkx as nx
import matplotlib.pyplot as plt
from torch_geometric.nn import SAGEConv
from torch_geometric.data import Data
from torch_geometric.utils import from_networkx, to_networkx
import itertools
import random
from sklearn.model_selection import train_test_split

from torch_geometric.loader import DataLoader
from torch_geometric.utils import get_laplacian
import sys

# --- 1. Setup: Create a single, fixed graph and problem set ---

# Create the fixed graph that will be used for everything
FIXED_GRAPH = nx.erdos_renyi_graph(n=15, p=0.3, seed=42)
while not nx.is_connected(FIXED_GRAPH): # Ensure it's connected
    FIXED_GRAPH = nx.erdos_renyi_graph(n=15, p=0.3)
edge_index = from_networkx(FIXED_GRAPH).edge_index
EDGE_COUNT = edge_index.size(1)
NODE_COUNT = FIXED_GRAPH.number_of_nodes()

# Generate all possible (start, end) pairs
all_nodes = list(FIXED_GRAPH.nodes)
all_possible_pairs = list(itertools.permutations(all_nodes, 2))

# Split pairs into a training set and a testing set
train_pairs, test_pairs = train_test_split(all_possible_pairs, test_size=0.2, random_state=42)

print(f"--- Graph Setup ---")
print(f"Fixed graph has {FIXED_GRAPH.number_of_nodes()} nodes and {FIXED_GRAPH.number_of_edges()} edges.")
print(f"Training on {len(train_pairs)} start/end pairs.")
print(f"Testing on {len(test_pairs)} unseen start/end pairs.")

# --- 2. Helper Function to Create a Specific Problem Instance ---
def generate_data(nx_graph, edge_index, start_node, end_node):
    # Calculate the shortest path for this specific problem
    path_nodes = nx.shortest_path(nx_graph, source=start_node, target=end_node)
    path_edges = set(zip(path_nodes[:-1], path_nodes[1:]))

    # Create edge labels (y)
    edge_labels = torch.zeros(EDGE_COUNT, dtype=torch.float)
    for i, (u, v) in enumerate(edge_index.t().tolist()):
        if (u, v) in path_edges or (v, u) in path_edges:
            edge_labels[i] = 1.0

    # Create node features (x) with start/end indicators
    node_features = torch.zeros((nx_graph.number_of_nodes(), 2))
    node_features[start_node, 0] = 1.0
    node_features[end_node, 1] = 1.0

    return Data(x=node_features,
                edge_index=edge_index.clone(),
                y=edge_labels,
                pair=torch.tensor([start_node, end_node], dtype=torch.long))


def soft_connectivity_penalty(batch, edge_probs, max_steps=None, clamp_prob=1.0):
    """Penalize batches whose predicted edges fail to connect start/end nodes."""
    num_graphs = batch.num_graphs
    if num_graphs == 0:
        return torch.tensor(0.0, device=edge_probs.device)

    if max_steps is None:
        max_steps = NODE_COUNT

    # Reshape edge probabilities to [num_graphs, EDGE_COUNT]
    edge_probs = edge_probs.view(num_graphs, EDGE_COUNT)

    # Build dense adjacency per graph from the canonical edge ordering
    adj = torch.zeros((num_graphs, NODE_COUNT * NODE_COUNT), device=edge_probs.device, dtype=edge_probs.dtype)
    rows = edge_index[0].to(edge_probs.device)
    cols = edge_index[1].to(edge_probs.device)
    flat_indices_forward = rows * NODE_COUNT + cols
    flat_indices_reverse = cols * NODE_COUNT + rows

    adj[:, flat_indices_forward] = edge_probs
    adj[:, flat_indices_reverse] = torch.maximum(adj[:, flat_indices_reverse], edge_probs)

    adj = adj.view(num_graphs, NODE_COUNT, NODE_COUNT)
    if adj.size(0) != num_graphs:
        raise ValueError(f"Adjacency batch mismatch: expected {num_graphs}, got {adj.size(0)}")
    adj = torch.clamp(adj, 0.0, clamp_prob)
    if torch.isnan(adj).any():
        raise ValueError("NaNs in adjacency")

    pairs = batch.pair
    if pairs.dim() == 1:
        pairs = pairs.unsqueeze(0)

    num_nodes = adj.size(-1)
    start = torch.nn.functional.one_hot(pairs[:, 0], num_classes=num_nodes).float().to(adj.device)
    end = torch.nn.functional.one_hot(pairs[:, 1], num_classes=num_nodes).float().to(adj.device)

    reached = start.clone()
    frontier = start.clone()

    for _ in range(max_steps):
        frontier = torch.einsum('bi,bij->bj', frontier, adj)
        reached = torch.clamp(reached + frontier, max=1.0)
        frontier = torch.clamp(frontier, max=1.0)

    connectivity = (reached * end).sum(dim=1)
    penalty = 1.0 - connectivity
    return penalty.mean()


def flow_connectivity_penalty(batch, edge_probs, clamp_prob=1.0, solver_reg=1e-4):
    """Encourage a unit flow from start to end using edge probabilities as capacities."""
    num_graphs = batch.num_graphs
    if num_graphs == 0:
        return torch.tensor(0.0, device=edge_probs.device)

    edge_probs = edge_probs.view(num_graphs, EDGE_COUNT)
    row = edge_index[0]
    col = edge_index[1]

    incidence = torch.zeros((NODE_COUNT, EDGE_COUNT), device=edge_probs.device)
    incidence[row, torch.arange(EDGE_COUNT, device=edge_probs.device)] = 1.0
    incidence[col, torch.arange(EDGE_COUNT, device=edge_probs.device)] -= 1.0

    penalties = []
    for graph_idx in range(num_graphs):
        probs = torch.clamp(edge_probs[graph_idx], min=0.0, max=clamp_prob)
        pairs = batch.pair[graph_idx]
        start, end = pairs.tolist()

        flow_vars = probs
        flows_pos = torch.relu(flow_vars)
        flow_matrix = incidence @ torch.diag(flows_pos)

        divergence = flow_matrix.sum(dim=1)
        divergence[start] -= 1.0
        divergence[end] += 1.0

        penalties.append(divergence.pow(2).mean())

    penalty = torch.stack(penalties).mean()
    return penalty

# --- 3. GNN Model Definition (No changes here) ---
class PathPredictorGNN(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels):
        super(PathPredictorGNN, self).__init__()
        self.conv1 = SAGEConv(in_channels, hidden_channels)
        self.conv2 = SAGEConv(hidden_channels, hidden_channels)
        self.edge_predictor = torch.nn.Sequential(
            torch.nn.Linear(2 * hidden_channels, hidden_channels),
            torch.nn.ReLU(),
            torch.nn.Linear(hidden_channels, out_channels),
            torch.nn.Sigmoid()
        )

    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        x = F.relu(self.conv1(x, edge_index))
        x = F.relu(self.conv2(x, edge_index))
        edge_src, edge_dst = edge_index
        edge_features = torch.cat([x[edge_src], x[edge_dst]], dim=1)
        return self.edge_predictor(edge_features).view(-1)

# --- 4. Training Loop ---
model = PathPredictorGNN(in_channels=2, hidden_channels=32, out_channels=1)
optimizer = torch.optim.Adam(model.parameters(), lr=0.005)
criterion = torch.nn.BCELoss()
CONNECTIVITY_WEIGHT = 0.1
CONNECTIVITY_MODE = "soft"  # options: "soft", "flow"

print("\n--- Starting Training ---")
train_dataset = [generate_data(FIXED_GRAPH, edge_index, s, e) for s, e in train_pairs]
test_dataset = [generate_data(FIXED_GRAPH, edge_index, s, e) for s, e in test_pairs]

train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=64)

for epoch in range(1, 1001):
    model.train()
    total_loss = 0.0

    for batch in train_loader:
        optimizer.zero_grad()
        edge_predictions = model(batch)
        bce_loss = criterion(edge_predictions, batch.y)
        if CONNECTIVITY_MODE == "soft":
            connectivity_loss = soft_connectivity_penalty(batch, edge_predictions)
        elif CONNECTIVITY_MODE == "flow":
            connectivity_loss = flow_connectivity_penalty(batch, edge_predictions)
        else:
            raise ValueError(f"Unknown CONNECTIVITY_MODE: {CONNECTIVITY_MODE}")
        loss = bce_loss + CONNECTIVITY_WEIGHT * connectivity_loss
        loss.backward()
        optimizer.step()
        total_loss += loss.item()

    avg_loss = total_loss / len(train_loader)
    if epoch % 10 == 0:
        print(f"Epoch: {epoch:03d}, Avg. Training Loss: {avg_loss:.4f}")

print("--- Training Finished ---")

# --- 5. Evaluation on the Unseen Test Set ---
@torch.no_grad()
def evaluate_model(model, loader):
    model.eval()
    correct_predictions = 0

    for batch in loader:
        edge_probs = model(batch)
        predicted_edges = (edge_probs > 0.5).float()
        labels = batch.y

        batch_size = batch.num_graphs
        preds_per_graph = predicted_edges.view(batch_size, EDGE_COUNT)
        labels_per_graph = labels.view(batch_size, EDGE_COUNT)

        matches = torch.all(preds_per_graph == labels_per_graph, dim=1)
        correct_predictions += matches.sum().item()

    accuracy = correct_predictions / len(loader.dataset)
    return accuracy


accuracy = evaluate_model(model, test_loader)
print(f"\n--- Evaluation Results ---")
print(f"Accuracy on unseen test pairs: {accuracy:.2%}")

sys.exit(0)
# --- 6. Visualize random test cases in a loop ---
print("Visualizing random test cases. Press Ctrl-C to stop...")
pos = nx.spring_layout(FIXED_GRAPH, seed=42)  # Keep consistent layout

try:
    while True:
        start, end = random.choice(test_pairs)
        data = generate_data(FIXED_GRAPH, edge_index, start, end)
        edge_probs = model(data)
        predicted_edges = edge_index[:, edge_probs > 0.5].t().tolist()
        actual_path_nodes = nx.shortest_path(FIXED_GRAPH, source=start, target=end)

        plt.figure(figsize=(12, 5))

        # Plot Ground Truth
        plt.subplot(1, 2, 1)
        nx.draw(FIXED_GRAPH, pos, with_labels=True, node_color='lightblue', edge_color='gray')
        path_edges = list(zip(actual_path_nodes, actual_path_nodes[1:]))
        nx.draw_networkx_edges(FIXED_GRAPH, pos, edgelist=path_edges, edge_color='green', width=3)
        nx.draw_networkx_nodes(FIXED_GRAPH, pos, nodelist=[start, end], node_color='green')
        plt.title(f'Ground Truth: {start} to {end}')

        # Plot GNN Prediction
        plt.subplot(1, 2, 2)
        nx.draw(FIXED_GRAPH, pos, with_labels=True, node_color='lightblue', edge_color='gray')
        nx.draw_networkx_edges(FIXED_GRAPH, pos, edgelist=predicted_edges, edge_color='red', width=3)
        nx.draw_networkx_nodes(FIXED_GRAPH, pos, nodelist=[start, end], node_color='red')
        plt.title(f'GNN Prediction: {start} to {end}')

        plt.show()

        # Optional: add a small delay between visualizations
        # time.sleep(1)

except KeyboardInterrupt:
    print("\nVisualization stopped by user.")
