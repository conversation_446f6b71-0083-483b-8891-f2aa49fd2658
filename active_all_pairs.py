import argparse
import copy
import json
import math
import os
import random
import time
from collections import defaultdict
from typing import Dict, Iterable, List, Sequence, Tuple

import networkx as nx
import torch
from torch_geometric.data import Data
from torch_geometric.loader import DataLoader

import shortest_path4 as sp4


Pair = Tuple[int, int]


def configure_shortest_path_module(graph: nx.DiGraph) -> None:
    """Update shortest_path4 module globals to use the provided graph."""
    sp4.FIXED_GRAPH = graph
    sp4.REVERSED_GRAPH = graph.reverse()
    pyg_graph = sp4.from_networkx(graph)
    sp4.edge_index = pyg_graph.edge_index
    sp4.edge_weight = torch.tensor(
        [graph[u][v]["weight"] for u, v in sp4.edge_index.t().tolist()],
        dtype=torch.float,
    )
    sp4.EDGE_COUNT = sp4.edge_index.size(1)
    sp4.NODE_COUNT = graph.number_of_nodes()


def enumerate_pairs(graph: nx.DiGraph) -> List[Pair]:
    nodes = list(graph.nodes)
    return [(s, t) for s in nodes for t in nodes if s != t]


def train_val_split(pairs: Sequence[Pair], val_ratio: float, seed: int) -> Tuple[List[Pair], List[Pair]]:
    rng = random.Random(seed)
    pairs = list(pairs)
    rng.shuffle(pairs)
    val_size = max(1, int(len(pairs) * val_ratio))
    val_pairs = pairs[:val_size]
    train_candidates = pairs[val_size:]
    return train_candidates, val_pairs


def build_seed_training_pairs(graph: nx.DiGraph, pool: Sequence[Pair], seed: int) -> List[Pair]:
    """Ensure each node appears as source and destination at least once."""
    rng = random.Random(seed)
    pool_set = set(pool)
    nodes = list(graph.nodes)
    selected = set()

    for src in nodes:
        candidates = [pair for pair in pool if pair[0] == src]
        if not candidates:
            continue
        selected.add(rng.choice(candidates))

    for dst in nodes:
        candidates = [pair for pair in pool if pair[1] == dst]
        if not candidates:
            continue
        selected.add(rng.choice(candidates))

    return list(selected)


class DataCache:
    def __init__(self, graph: nx.DiGraph):
        self.graph = graph
        self.store: Dict[Pair, Data] = {}

    def __contains__(self, pair: Pair) -> bool:
        return pair in self.store

    def get(self, pair: Pair) -> Data:
        if pair not in self.store:
            data = sp4.generate_data(self.graph, sp4.edge_index, pair[0], pair[1])
            self.store[pair] = data
        return self.store[pair]

    def get_many(self, pairs: Iterable[Pair]) -> List[Data]:
        return [self.get(pair) for pair in pairs]


@torch.no_grad()
def evaluate_with_threshold(
    model: torch.nn.Module,
    loader: DataLoader,
    threshold: float,
    device: torch.device,
) -> float:
    model.eval()
    correct = 0
    total = 0
    for batch in loader:
        batch = batch.to(device)
        logits = model(batch)
        probs = torch.sigmoid(logits)
        preds = (probs >= threshold).float()
        labels = batch.y
        batch_size = batch.num_graphs
        correct += torch.all(
            preds.view(batch_size, sp4.EDGE_COUNT) == labels.view(batch_size, sp4.EDGE_COUNT), dim=1
        ).sum().item()
        total += batch_size
    return correct / max(1, total)


def search_probability_threshold(
    model: torch.nn.Module,
    loader: DataLoader,
    prob_grid: Sequence[float],
    device: torch.device,
) -> Tuple[float, float]:
    best_threshold = prob_grid[0]
    best_acc = -math.inf
    for thr in prob_grid:
        acc = evaluate_with_threshold(model, loader, thr, device)
        if acc > best_acc + 1e-6:
            best_acc = acc
            best_threshold = thr
    return best_threshold, best_acc


def train_once(
    train_dataset: List[Data],
    val_dataset: List[Data],
    prob_grid: Sequence[float],
    max_epochs: int,
    eval_interval: int,
    patience: int,
    lr: float,
    weight_decay: float,
    connectivity_weight: float,
    length_weight: float,
    batch_size: int,
    val_batch_size: int,
) -> Dict[str, object]:
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = sp4.PathPredictorGNN(in_channels=5, hidden_channels=64, out_channels=1).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=val_batch_size)

    # Prepare class-balanced BCE loss
    total_pos = sum(data.y.sum().item() for data in train_dataset)
    total_edges = len(train_dataset) * sp4.EDGE_COUNT
    total_neg = max(total_edges - total_pos, 1.0)
    pos_weight = torch.tensor(total_neg / max(total_pos, 1.0), dtype=torch.float, device=device)
    bce_loss_fn = torch.nn.BCEWithLogitsLoss(pos_weight=pos_weight)

    best_state = None
    best_val_acc = -math.inf
    best_threshold = prob_grid[0]
    epochs_without_improvement = 0
    final_epoch = 0

    for epoch in range(1, max_epochs + 1):
        model.train()
        total_loss = 0.0
        for batch in train_loader:
            batch = batch.to(device)
            optimizer.zero_grad()
            logits = model(batch)
            edge_probs = torch.sigmoid(logits)
            bce_loss = bce_loss_fn(logits, batch.y)
            conn_loss = sp4.soft_connectivity_penalty(batch, edge_probs)
            len_loss = sp4.path_length_penalty(batch, edge_probs)
            loss = bce_loss + connectivity_weight * conn_loss + length_weight * len_loss
            loss.backward()
            optimizer.step()
            total_loss += loss.item()

        avg_loss = total_loss / max(1, len(train_loader))
        if epoch % 100 == 0:
            print(f"Epoch: {epoch:03d}, Avg. Training Loss: {avg_loss:.4f}")

        final_epoch = epoch
        if epoch % eval_interval == 0:
            model.eval()
            with torch.no_grad():
                thr, val_acc = search_probability_threshold(model, val_loader, prob_grid, device)
            improved = val_acc > best_val_acc + 1e-6
            status = "*" if improved else ""
            print(f"Epoch: {epoch:03d}, Val Accuracy: {val_acc:.2%}{status}")
            if improved:
                best_val_acc = val_acc
                best_threshold = thr
                best_state = copy.deepcopy(model.state_dict())
                epochs_without_improvement = 0
            else:
                epochs_without_improvement += 1
                if epochs_without_improvement >= patience:
                    print(f"Early stopping triggered at epoch {epoch}.")
                    break

    if best_state is not None:
        model.load_state_dict(best_state)

    val_threshold, val_accuracy = search_probability_threshold(model, val_loader, prob_grid, device)

    return {
        "model": model,
        "val_accuracy": val_accuracy,
        "best_prob_threshold": val_threshold,
        "epochs_trained": final_epoch,
    }


def edge_mask_to_path(edge_index: torch.Tensor, mask: torch.Tensor, source: int, target: int) -> List[int]:
    selected = defaultdict(list)
    for idx, flag in enumerate(mask.tolist()):
        if flag:
            u = int(edge_index[0, idx])
            v = int(edge_index[1, idx])
            selected[u].append(v)
    path = [source]
    current = source
    visits = 0
    while current != target and visits <= len(mask):
        neighbors = selected.get(current, [])
        if len(neighbors) != 1:
            return []
        current = neighbors[0]
        path.append(current)
        visits += 1
    if current != target:
        return []
    return path


def symmetric_difference_size(a: torch.Tensor, b: torch.Tensor) -> int:
    return int((a != b).sum().item())


def acquire_pairs(
    model: torch.nn.Module,
    prob_threshold: float,
    candidate_pairs: List[Pair],
    data_cache: DataCache,
    acquisition_size: int,
    device: torch.device,
) -> List[Pair]:
    if not candidate_pairs:
        return []

    model.eval()
    scores: List[Tuple[int, Pair]] = []
    for pair in candidate_pairs:
        data = data_cache.get(pair).to(device)
        with torch.no_grad():
            logits = model(data)
            edge_probs = torch.sigmoid(logits)
        pred_mask = (edge_probs >= prob_threshold).float().cpu()
        gold_mask = data.y.cpu()
        diff = symmetric_difference_size(pred_mask, gold_mask)
        scores.append((diff, pair))

    scores.sort(key=lambda x: x[0], reverse=True)
    selected = [pair for diff, pair in scores if diff > 0]
    if len(selected) < acquisition_size:
        remaining = [pair for diff, pair in scores if diff == 0]
        selected.extend(remaining)
    return selected[:acquisition_size]


def infer_all_pairs(
    model: torch.nn.Module,
    prob_threshold: float,
    pairs: Sequence[Pair],
    data_cache: DataCache,
    device: torch.device,
) -> List[Dict[str, object]]:
    results = []
    model.eval()
    for pair in pairs:
        data = data_cache.get(pair).to(device)
        with torch.no_grad():
            logits = model(data)
            edge_probs = torch.sigmoid(logits)
        pred_mask = (edge_probs >= prob_threshold).float().cpu()
        path = edge_mask_to_path(sp4.edge_index, pred_mask, pair[0], pair[1])
        gold_mask = data.y.cpu()
        is_correct = bool(torch.all(pred_mask == gold_mask))
        if not path:
            # Fallback to Dijkstra if mask does not form a valid path
            nodes = nx.shortest_path(sp4.FIXED_GRAPH, source=pair[0], target=pair[1], weight="weight")
            path = [int(n) for n in nodes]
        results.append({
            "source": pair[0],
            "target": pair[1],
            "path": path,
            "is_correct": is_correct,
        })
    return results


def ensure_dir(path: str) -> None:
    os.makedirs(path, exist_ok=True)


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Active learning for all-pairs shortest path prediction")
    parser.add_argument("--num-nodes", type=int, default=15)
    parser.add_argument("--edge-prob", type=float, default=0.3)
    parser.add_argument("--graph-seed", type=int, default=42)
    parser.add_argument("--val-ratio", type=float, default=0.2)
    parser.add_argument("--target-accuracy", type=float, default=0.99)
    parser.add_argument("--candidate-pool", type=int, default=100)
    parser.add_argument("--acquisition-size", type=int, default=10)
    parser.add_argument("--max-iterations", type=int, default=20)
    parser.add_argument("--max-epochs", type=int, default=2000)
    parser.add_argument("--eval-interval", type=int, default=100)
    parser.add_argument("--patience", type=int, default=8)
    parser.add_argument("--lr", type=float, default=0.003)
    parser.add_argument("--weight-decay", type=float, default=0.0)
    parser.add_argument("--connectivity-weight", type=float, default=0.1)
    parser.add_argument("--length-weight", type=float, default=0.1)
    parser.add_argument("--batch-size", type=int, default=32)
    parser.add_argument("--val-batch-size", type=int, default=64)
    parser.add_argument("--acquisition-batch-size", type=int, default=50, help="Number of candidates evaluated per acquisition round")
    parser.add_argument("--artifacts-dir", type=str, default="artifacts")
    parser.add_argument("--prediction-file", type=str, default="artifacts/all_pairs_predictions.jsonl")
    parser.add_argument("--config-file", type=str, default="artifacts/active_training_config.json")
    parser.add_argument("--model-file", type=str, default="artifacts/active_model.pth")
    parser.add_argument("--random-seed", type=int, default=1234)
    return parser.parse_args()


def main() -> None:
    args = parse_args()
    random.seed(args.random_seed)
    torch.manual_seed(args.random_seed)

    # Step 1: Graph setup and module configuration
    graph = sp4.make_directed_weighted_graph(num_nodes=args.num_nodes, prob=args.edge_prob, seed=args.graph_seed, directed=True)
    configure_shortest_path_module(graph)

    all_pairs = enumerate_pairs(graph)
    unlabeled_pool, val_pairs = train_val_split(all_pairs, args.val_ratio, args.random_seed)
    print(f"Total OD pairs: {len(all_pairs)} | Validation pairs: {len(val_pairs)} | Initial pool: {len(unlabeled_pool)}")

    data_cache = DataCache(graph)
    val_dataset = data_cache.get_many(val_pairs)

    seed_pairs = build_seed_training_pairs(graph, unlabeled_pool, args.random_seed)
    labeled_train = set(seed_pairs)
    for pair in seed_pairs:
        data_cache.get(pair)
    print(f"Seed training set size: {len(labeled_train)}")

    unlabeled_set = set(unlabeled_pool) - labeled_train

    prob_grid = [round(x, 2) for x in torch.linspace(0.05, 0.95, steps=19).tolist()]
    if 0.5 not in prob_grid:
        prob_grid.append(0.5)
    prob_grid = sorted(set(prob_grid))

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    metrics_log = []
    model = None
    best_prob_threshold = prob_grid[0]

    for iteration in range(1, args.max_iterations + 1):
        print(f"\n=== Iteration {iteration} ===")
        train_dataset = data_cache.get_many(labeled_train)
        start_time = time.time()
        result = train_once(
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            prob_grid=prob_grid,
            max_epochs=args.max_epochs,
            eval_interval=args.eval_interval,
            patience=args.patience,
            lr=args.lr,
            weight_decay=args.weight_decay,
            connectivity_weight=args.connectivity_weight,
            length_weight=args.length_weight,
            batch_size=args.batch_size,
            val_batch_size=args.val_batch_size,
        )
        duration = time.time() - start_time

        model = result["model"]
        best_prob_threshold = result["best_prob_threshold"]
        val_accuracy = result["val_accuracy"]
        epochs_trained = result["epochs_trained"]

        metrics_log.append({
            "iteration": iteration,
            "train_size": len(labeled_train),
            "val_accuracy": val_accuracy,
            "epochs_trained": epochs_trained,
            "duration_sec": duration,
            "prob_threshold": best_prob_threshold,
        })

        print(f"Iteration {iteration} complete: val_accuracy={val_accuracy:.2%}, prob_threshold={best_prob_threshold:.2f}")

        if val_accuracy >= args.target_accuracy or not unlabeled_set:
            print("Target accuracy reached or no unlabeled pairs remain. Stopping acquisition.")
            break

        candidate_list = random.sample(list(unlabeled_set), min(args.acquisition_batch_size, len(unlabeled_set)))
        new_pairs = acquire_pairs(
            model=model.to(device),
            prob_threshold=best_prob_threshold,
            candidate_pairs=candidate_list,
            data_cache=data_cache,
            acquisition_size=min(args.acquisition_size, len(unlabeled_set)),
            device=device,
        )
        if not new_pairs:
            print("No informative pairs found; stopping.")
            break

        for pair in new_pairs:
            labeled_train.add(pair)
            unlabeled_set.discard(pair)
            data_cache.get(pair)
        print(f"Acquired {len(new_pairs)} new pairs. Labeled set size: {len(labeled_train)}")

    if model is None:
        raise RuntimeError("Training did not run; check configuration.")

    ensure_dir(os.path.dirname(args.model_file))
    torch.save({
        "state_dict": model.state_dict(),
        "prob_threshold": best_prob_threshold,
        "config": vars(args),
    }, args.model_file)
    print(f"Saved model to {args.model_file}")

    all_results = infer_all_pairs(model.to(device), best_prob_threshold, all_pairs, data_cache, device)

    ensure_dir(os.path.dirname(args.prediction_file))
    with open(args.prediction_file, "w") as f:
        for record in all_results:
            f.write(json.dumps(record) + "\n")
    print(f"Wrote predictions to {args.prediction_file}")

    ensure_dir(os.path.dirname(args.config_file))
    summary = {
        "graph_nodes": graph.number_of_nodes(),
        "graph_edges": graph.number_of_edges(),
        "total_pairs": len(all_pairs),
        "final_train_pairs": len(labeled_train),
        "validation_pairs": len(val_pairs),
        "best_prob_threshold": best_prob_threshold,
        "target_accuracy": args.target_accuracy,
        "final_val_accuracy": val_accuracy,
        "iterations": metrics_log,
    }
    with open(args.config_file, "w") as f:
        json.dump(summary, f, indent=2)
    print(f"Saved training summary to {args.config_file}")


if __name__ == "__main__":
    main()
